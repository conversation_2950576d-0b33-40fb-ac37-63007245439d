import NextAuth from "next-auth";
import { ZodError } from "zod";
import Credentials from "next-auth/providers/credentials";
import Google from "next-auth/providers/google"
import GitHub from "next-auth/providers/github"
import { signInSchema } from "@/app/lib/zod";
import { verifyPassword } from "@/app/utils/password";
import { db } from '@/app/db';
import { users } from '@/app/db/schema';
import { eq } from 'drizzle-orm';

export const { handlers, signIn, signOut, auth } = NextAuth({
  providers: [
    Google,
    GitHub,
    Credentials({
      // You can specify which fields should be submitted, by adding keys to the `credentials` object.
      // e.g. domain, username, password, 2FA token, etc.
      credentials: {
        email: {},
        password: {},
      },
      authorize: async (credentials) => {
        try {
          const { email, password } = await signInSchema.parseAsync(credentials);
          const user = await db.query.users
            .findFirst({
              where: eq(users.email, email)
            })
          if (!user || !user.password) {
            return null;
          }
          const passwordMatch = await verifyPassword(password, user.password);
          if (passwordMatch) {
            return {
              id: user.id,
              name: user.name,
              email: user.email,
              isAdmin: user.isAdmin || false,
            };
          } else {
            return null;
          }
        } catch (error) {
          if (error instanceof ZodError) {
            // 如果验证失败，返回 null 表示凭据无效
            return null;
          }
          // 处理其他错误
          throw error;
        }
      },
    }),

  ],
  pages: {
    error: '/auth/error', // 自定义错误页面
  },
  callbacks: {
    async jwt({ token, user, account }) {
      if (user) {
        token.id = user.id;
        token.isAdmin = user.isAdmin;
      }
      if (account?.provider === "credentials" && token.sub) {
        token.provider = 'credentials';
      }
      if (account?.provider === "dingding" && token.sub) {
        const dbUser = await db.query.users.findFirst({
          where: eq(users.dingdingUnionId, account.providerAccountId)
        });

        if (dbUser) {
          token.id = dbUser.id;
          token.isAdmin = dbUser.isAdmin || false;
        }
        token.provider = 'dingding';
      }
      if (account?.provider === "github" && token.sub) {
        // Github 登录时，使用 email 查找用户
        const dbUser = await db.query.users.findFirst({
          where: eq(users.email, token.email as string)
        });

        if (dbUser) {
          token.id = dbUser.id;
          token.isAdmin = dbUser.isAdmin || false;
        } else {
          // 如果用户不存在，创建新用户
          const newUser = await db.insert(users).values({
            email: token.email as string,
            name: token.name as string,
            image: token.picture as string,
          }).returning();

          if (newUser[0]) {
            token.id = newUser[0].id;
            token.isAdmin = newUser[0].isAdmin || false;
          }
        }
        token.provider = 'github';
      }
      if (account?.provider === "google" && token.sub) {
        // Google 登录时，使用 email 查找用户
        const dbUser = await db.query.users.findFirst({
          where: eq(users.email, token.email as string)
        });

        if (dbUser) {
          token.id = dbUser.id;
          token.isAdmin = dbUser.isAdmin || false;
        } else {
          // 如果用户不存在，创建新用户
          const newUser = await db.insert(users).values({
            email: token.email as string,
            name: token.name as string,
            image: token.picture as string,
          }).returning();

          if (newUser[0]) {
            token.id = newUser[0].id;
            token.isAdmin = newUser[0].isAdmin || false;
          }
        }
        token.provider = 'google';
      }
      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user = {
          ...session.user, // 保留已有的属性
          id: String(token.id),
          isAdmin: Boolean(token.isAdmin), // 添加 isAdmin
          provider: token.provider as string,
        };
      }
      return session;
    },
  },
})