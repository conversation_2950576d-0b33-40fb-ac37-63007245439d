'use server';
import { db } from '@/app/db';
import { eq, and, asc } from 'drizzle-orm';
import { LLMModel, LLMModelProvider } from '@/types/llm';
import { llmSettingsTable, llmModels, groupModels, groups, users } from '@/app/db/schema';
import { llmModelType } from '@/app/db/schema';
import { getLlmConfigByProvider } from '@/app/utils/llms';
import { getWorkspaceFromFormData, isWorkspaceAdmin, createErrorResponse, createSuccessResponse } from '@/app/utils/workspace';
import { auth } from '@/auth';

type FormValues = {
  isActive?: boolean;
  apikey?: string;
  providerName?: string;
  endpoint?: string;
  order?: number;
}
export const saveToServer = async (formData: FormData, workspaceId: string) => {
  // 验证workspace管理员权限
  const adminValidation = await isWorkspaceAdmin(workspaceId);
  if (!adminValidation) {
    return createErrorResponse('Access denied');
  }

  const providerId = formData.get('providerId') as string;
  const values: FormValues = {
    isActive: formData.get('isActive') === 'true',
    apikey: formData.get('apikey') as string || undefined,
    providerName: formData.get('providerName') as string || undefined,
    endpoint: formData.get('endpoint') as string || undefined,
    order: formData.get('order') ? parseInt(formData.get('order') as string) : undefined,
  };

  if (!providerId) {
    return createErrorResponse('Provider ID is required');
  }

  try {
    const existingRecord = await db.select().from(llmSettingsTable)
      .where(
        and(
          eq(llmSettingsTable.provider, providerId),
          eq(llmSettingsTable.workspaceId, workspaceId)
        )
      )
      .limit(1);

    if (existingRecord.length > 0) {
      await db.update(llmSettingsTable)
        .set(values)
        .where(
          and(
            eq(llmSettingsTable.provider, providerId),
            eq(llmSettingsTable.workspaceId, workspaceId)
          )
        );
    } else {
      // 如果记录不存在，插入新记录
      await db.insert(llmSettingsTable)
        .values({
          provider: providerId,
          providerName: values.providerName || 'Untitled',
          workspaceId,
          ...values
        });
    }

    return createSuccessResponse();
  } catch (error) {
    console.error('Error saving provider settings:', error);
    return createErrorResponse('Failed to save provider settings');
  }
};

export const fetchAllProviders = async (workspaceId: string) => {
  const settings = await db.select({
    provider: llmSettingsTable.provider,
    providerName: llmSettingsTable.providerName,
    isActive: llmSettingsTable.isActive,
    apiStyle: llmSettingsTable.apiStyle,
    logo: llmSettingsTable.logo,
  })
    .from(llmSettingsTable)
    .where(eq(llmSettingsTable.workspaceId, workspaceId));
  return settings;
}

export const fetchAllLlmSettings = async (workspaceId: string) => {
  const adminValidation = await isWorkspaceAdmin(workspaceId);
  if (!adminValidation) {
    return createErrorResponse('Access denied');
  }

  const validatedWorkspaceId = workspaceId;

  try {
    const settings = await db.select()
      .from(llmSettingsTable)
      .where(eq(llmSettingsTable.workspaceId, validatedWorkspaceId))
      .orderBy(asc(llmSettingsTable.order));

    return createSuccessResponse(settings);
  } catch (error) {
    console.error('Error fetching LLM settings:', error);
    return createErrorResponse('Failed to fetch LLM settings');
  }
}

export const fetchLlmModels = async (providerId?: string, workspaceId?: string): Promise<llmModelType[]> => {
  // 明确指定字段，避免类型错误
  const modelFields = {
    id: llmModels.id,
    name: llmModels.name,
    displayName: llmModels.displayName,
    maxTokens: llmModels.maxTokens,
    supportVision: llmModels.supportVision,
    supportTool: llmModels.supportTool,
    selected: llmModels.selected,
    providerId: llmModels.providerId,
    providerName: llmModels.providerName,
    type: llmModels.type,
    order: llmModels.order,
    createdAt: llmModels.createdAt,
    updatedAt: llmModels.updatedAt,
  };
  if (providerId) {
    const whereConditions = [eq(llmModels.providerId, providerId)];
    if (workspaceId) {
      whereConditions.push(eq(llmModels.workspaceId, workspaceId));
    }

    const result = await db
      .select({
        ...modelFields,
        providerLogo: llmSettingsTable.logo,
        apiStyle: llmSettingsTable.apiStyle,
      })
      .from(llmModels)
      .innerJoin(llmSettingsTable, and(
        eq(llmModels.providerId, llmSettingsTable.provider),
        workspaceId ? eq(llmSettingsTable.workspaceId, workspaceId) : undefined
      ))
      .where(and(...whereConditions))
      .orderBy(asc(llmModels.order), asc(llmModels.createdAt));
    return result.map((item: any) => ({
      ...item,
      providerLogo: item.providerLogo,
      apiStyle: item.apiStyle,
    }));
  } else {
    const result = await db
      .select({
        ...modelFields,
        providerLogo: llmSettingsTable.logo,
        apiStyle: llmSettingsTable.apiStyle,
      })
      .from(llmModels)
      .innerJoin(llmSettingsTable, eq(llmModels.providerId, llmSettingsTable.provider));
    return result.map((item: any) => ({
      ...item,
      providerLogo: item.providerLogo,
      apiStyle: item.apiStyle,
    }));
  }
}

export const getProviderById = async (providerId: string, workspaceId: string): Promise<LLMModelProvider> => {
  const adminValidation = await isWorkspaceAdmin(workspaceId);
  if (!adminValidation) {
    const error = new Error(`Access denied`);
    throw error;
  }

  const result = await db.select().from(llmSettingsTable).where(
    and(
      eq(llmSettingsTable.provider, providerId),
      eq(llmSettingsTable.workspaceId, workspaceId)
    )
  );

  if (!result || result.length === 0) {
    const error = new Error(`Provider with ID '${providerId}' not found`);
    (error as any).status = 404;
    throw error;
  }

  const dbProvider = result[0];

  // 将数据库字段映射到 LLMModelProvider 类型
  return {
    id: dbProvider.provider,
    providerName: dbProvider.providerName,
    apiStyle: dbProvider.apiStyle,
    providerLogo: dbProvider.logo || undefined,
    status: dbProvider.isActive || false,
    type: dbProvider.type || 'default'
  };
}

export const fetchAvailableProviders = async (workspaceId: string) => {
  const availableProviders = await db.select().from(llmSettingsTable).where(
    and(
      eq(llmSettingsTable.isActive, true),
      eq(llmSettingsTable.workspaceId, workspaceId)
    )
  );
  return availableProviders;
}
const getUserModels = async (): Promise<number[]> => {
  const session = await auth();
  const userId = session?.user.id;
  if (!userId) return [];
  const dbUserInfo = await db.query.users.findFirst({
    where: eq(users.id, userId)
  });
  const groupId = dbUserInfo?.groupId;
  if (!groupId) {
    return (await db.query.llmModels.findMany({
      columns: { id: true }
    })).map(m => m.id);
  }

  const group = await db.query.groups.findFirst({
    where: eq(groups.id, groupId),
  });

  return group?.modelType === 'all'
    ? (await db.query.llmModels.findMany({ columns: { id: true } })).map(m => m.id)
    : (await db.query.groupModels.findMany({
      where: eq(groupModels.groupId, groupId),
      columns: { modelId: true },
    })).map(m => m.modelId);

}
export const fetchAvailableLlmModels = async (workspaceId: string, requireAuth: boolean = true): Promise<llmModelType[]> => {
  const userModels = requireAuth ? new Set(await getUserModels()) : new Set<number>();
  const result = await db
    .select()
    .from(llmSettingsTable)
    .innerJoin(llmModels, and(
      eq(llmSettingsTable.provider, llmModels.providerId),
      eq(llmSettingsTable.workspaceId, llmModels.workspaceId)
    ))
    .orderBy(
      asc(llmSettingsTable.order),
      asc(llmModels.order),
    )
    .where(
      and(
        eq(llmSettingsTable.isActive, true),
        eq(llmModels.selected, true),
        eq(llmSettingsTable.workspaceId, workspaceId),
        eq(llmModels.workspaceId, workspaceId)
      )
    );
  const llmModelList: llmModelType[] = result
    .map((i) => {
      return {
        ...i.models,
        id: i.models?.id ?? 0,
        providerName: i.llm_settings.providerName,
        providerLogo: i.llm_settings.logo || '',
        apiStyle: i.llm_settings.apiStyle,
      }
    })
    .filter((model) => model !== null && (!requireAuth || userModels.has(model.id)));
  return llmModelList;
}

export const changeSelectInServer = async (modelName: string, selected: boolean, workspaceId: string) => {
  await db.update(llmModels)
    .set({
      selected: selected,
    })
    .where(
      and(
        eq(llmModels.name, modelName),
        eq(llmModels.workspaceId, workspaceId)
      )
    )
}

export const changeModelSelectInServer = async (model: LLMModel, selected: boolean, workspaceId: string) => {
  const hasExist = await db.select()
    .from(llmModels)
    .where(
      and(
        eq(llmModels.name, model.id),
        eq(llmModels.providerId, model.provider.id),
        eq(llmModels.workspaceId, workspaceId)
      )
    )
  if (hasExist.length > 0) {
    await db.update(llmModels)
      .set({
        selected: selected,
      })
      .where(
        and(
          eq(llmModels.name, model.id),
          eq(llmModels.workspaceId, workspaceId)
        )
      )
  } else {
    await db.insert(llmModels).values({
      name: model.id,
      displayName: model.displayName,
      selected: selected,
      type: 'default',
      providerId: model.provider.id,
      providerName: model.provider.providerName,
      order: 100,
      workspaceId,
    })
  }
}

export const deleteCustomModelInServer = async (modelName: string, workspaceId: string) => {
  const adminValidation = await isWorkspaceAdmin(workspaceId);
  if (!adminValidation) {
    throw new Error('Access denied');
  }

  await db.delete(llmModels).where(
    and(
      eq(llmModels.name, modelName),
      eq(llmModels.workspaceId, workspaceId)
    )
  );
}

export const addCustomModelInServer = async (modelInfo: {
  name: string,
  displayName: string,
  maxTokens: number,
  supportVision: boolean,
  supportTool: boolean,
  selected: boolean,
  type: 'custom',
  providerId: string,
  providerName: string,
}, workspaceId: string) => {
  const adminValidation = await isWorkspaceAdmin(workspaceId);
  if (!adminValidation) {
    return createErrorResponse('Access denied');
  }
  const hasExist = await db
    .select()
    .from(llmModels)
    .where(
      and(
        eq(llmModels.providerId, modelInfo.providerId),
        eq(llmModels.name, modelInfo.name),
        eq(llmModels.workspaceId, workspaceId)
      )
    );
  if (hasExist.length > 0) {
    return {
      status: 'fail',
      message: '已存在相同名称的模型'
    }
  }
  await db.insert(llmModels).values({
    ...modelInfo,
    workspaceId
  });
  return {
    status: 'success',
  }

}

export const updateCustomModelInServer = async (oldModelName: string, modelInfo: {
  name: string,
  displayName: string,
  maxTokens: number,
  supportVision: boolean,
  supportTool: boolean,
  selected: boolean,
  type: 'custom',
  providerId: string,
  providerName: string,
}, workspaceId: string) => {
  const adminValidation = await isWorkspaceAdmin(workspaceId);
  if (!adminValidation) {
    return createErrorResponse('Access denied');
  }
  const hasExist = await db
    .select()
    .from(llmModels)
    .where(
      and(
        eq(llmModels.providerId, modelInfo.providerId),
        eq(llmModels.name, oldModelName),
        eq(llmModels.workspaceId, workspaceId)
      )
    );
  if (hasExist.length === 0) {
    return {
      status: 'fail',
      message: '该模型已经被删除'
    }
  }
  await db
    .update(llmModels)
    .set(modelInfo)
    .where(
      and(
        eq(llmModels.providerId, modelInfo.providerId),
        eq(llmModels.name, oldModelName),
        eq(llmModels.workspaceId, workspaceId)
      )
    );
  return {
    status: 'success',
  }
}

export const addCustomProviderInServer = async (providerInfo: {
  provider: string,
  providerName: string,
  endpoint: string,
  apiStyle: 'openai' | 'openai_response' | 'claude' | 'gemini',
  apikey: string,
}, workspaceId: string) => {
  const adminValidation = await isWorkspaceAdmin(workspaceId);
  if (!adminValidation) {
    throw new Error('Access denied');
  }
  const hasExist = await db
    .select()
    .from(llmSettingsTable)
    .where(
      and(
        eq(llmSettingsTable.provider, providerInfo.provider),
        eq(llmSettingsTable.workspaceId, workspaceId)
      )
    );
  if (hasExist.length > 0) {
    return {
      status: 'fail',
      message: '已存在相同名称的模型'
    }
  }
  await db.insert(llmSettingsTable).values({
    ...providerInfo,
    type: 'custom',
    isActive: true,
    workspaceId
  });
  return {
    status: 'success',
  }
}

export const deleteCustomProviderInServer = async (formData: FormData) => {
  // 验证 workspace 权限
  const workspaceValidation = await getWorkspaceFromFormData(formData);
  if ('error' in workspaceValidation) {
    return workspaceValidation;
  }

  const { workspaceId } = workspaceValidation as { workspaceId: string; userId: string; role: string };
  const providerId = formData.get('providerId') as string;

  if (!providerId) {
    return createErrorResponse('Provider ID is required');
  }

  try {
    await db.delete(llmSettingsTable).where(
      and(
        eq(llmSettingsTable.provider, providerId),
        eq(llmSettingsTable.workspaceId, workspaceId)
      )
    );
    return createSuccessResponse();
  } catch (error) {
    console.error('Error deleting provider:', error);
    return createErrorResponse('Failed to delete provider');
  }
}

export const saveModelsOrder = async (
  providerId: string,
  newOrderModels: {
    modelId: string;
    order: number
  }[],
  workspaceId: string) => {
  // 验证workspace管理员权限
  const adminValidation = await isWorkspaceAdmin(workspaceId);
  if (!adminValidation) {
    throw new Error('Access denied');
  }
  const updatePromises = newOrderModels.map((item) => {
    return db
      .update(llmModels)
      .set({ order: item.order })
      .where(
        and(
          eq(llmModels.providerId, providerId),
          eq(llmModels.name, item.modelId),
          eq(llmModels.workspaceId, workspaceId)
        )
      )
  });

  // 执行所有更新操作
  await Promise.all(updatePromises);
}

export const saveProviderOrder = async (
  newOrderProviders: {
    providerId: string;
    order: number
  }[],
  workspaceId: string) => {
  const adminValidation = await isWorkspaceAdmin(workspaceId);
  if (!adminValidation) {
    throw new Error('Access denied');
  }

  const updatePromises = newOrderProviders.map((item) => {
    return db
      .update(llmSettingsTable)
      .set({ order: item.order })
      .where(
        and(
          eq(llmSettingsTable.provider, item.providerId),
          eq(llmSettingsTable.workspaceId, workspaceId)
        )
      )
  });

  // 执行所有更新操作
  await Promise.all(updatePromises);
}

export const getRemoteModelsByProvider = async (providerId: string, workspaceId: string): Promise<{
  id: string;
  object: string;
  created: number;
  owned_by: string;
}[]> => {
  const { endpoint, apikey } = await getLlmConfigByProvider(providerId, workspaceId);
  const apiUrl = endpoint + '/models';
  const headers = new Headers({
    'Content-Type': 'application/json',
    'Connection': 'keep-alive',
    'Authorization': `Bearer ${apikey}`,
  });
  try {
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: headers,
    });
    if (!response.ok) {
      return [];
    }
    const body = await response.json();
    return body.data;
  } catch {
    return [];
  }
}
